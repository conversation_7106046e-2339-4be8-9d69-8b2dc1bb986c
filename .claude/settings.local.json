{"permissions": {"allow": ["Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "Bash(npm run type-check:*)", "Bash(npm run build:*)", "Bash(npm pack:*)", "Bash(rm:*)", "Bash(node:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "mcp__puppeteer__puppeteer_navigate", "Bash(npx puppeteer browsers:*)", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "Bash(npm run dev:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(npm run lint)", "Bash(npm run format:*)", "mcp__sequential-thinking__sequentialthinking", "WebFetch(domain:www.npmjs.com)", "WebFetch(domain:github.com)", "mcp__filesystem__list_directory", "mcp__filesystem__read_multiple_files", "Bash(npx tsc:*)", "<PERSON><PERSON>(mv:*)", "Bash(bash:*)", "mcp__filesystem__write_file", "mcp__filesystem__read_file", "mcp__filesystem__directory_tree", "mcp__filesystem__create_directory", "WebFetch(domain:loglayer.dev)", "Bash(grep:*)", "Bash(npx tsup:*)", "Bash(npm test)", "Bash(npm run test:unit:*)", "Bash(npm run:*)", "Bash(tree:*)", "Bash(git tag:*)", "Bash(npm publish:*)", "Bash(npm version:*)", "<PERSON><PERSON>(echo:*)", "Bash(pnpm add:*)", "Bash(npx changeset:*)", "Bash(pnpm install:*)", "Bash(pnpm build:packages:*)", "Bash(pnpm dev:basic:*)", "Bash(pnpm dev:*)", "Bash(pnpm view:*)", "Bash(pnpm build:*)", "Bash(pnpm why:*)", "Bash(turbo build:*)", "Bash(pnpm turbo build:*)", "Bash(pnpm run:*)", "Bash(npm ls:*)", "Bash(pnpm ls:*)", "Bash(npm info:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(env)", "Bash(timeout 30s npm run dev)", "Bash(gtimeout 30s npm run dev)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(true)", "Bash(git checkout:*)", "Bash(gh release create:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "Bash(gh api:*)", "WebFetch(domain:raw.githubusercontent.com)", "<PERSON><PERSON>(touch:*)"], "deny": []}}