{"name": "loglayer-support-monorepo", "version": "0.8.0", "description": "企业级日志解决方案 Monorepo - 包含 @yai-loglayer/* 包", "private": true, "workspaces": ["packages/*", "examples/*", "tools/*"], "scripts": {"build": "turbo build", "build:packages": "turbo build --filter='./packages/*'", "dev": "turbo dev", "dev:react": "turbo dev --filter=react-example", "dev:nextjs": "turbo dev --filter=nextjs-example", "dev:basic": "turbo dev --filter=basic-example", "clean": "turbo clean", "type-check": "turbo type-check", "test": "turbo test", "test:packages": "turbo test --filter='./packages/*'", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\" --ignore-path .gitignore", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\" --ignore-path .gitignore", "changeset": "changeset", "changeset:version": "changeset version", "changeset:publish": "changeset publish", "release:beta": "pnpm build:packages && pnpm changeset:publish --tag beta", "release:latest": "pnpm build:packages && pnpm changeset:publish"}, "keywords": ["logging", "logger", "loglayer", "nextjs", "typescript", "pino", "winston", "compatibility"], "author": "YAI Nexus Team", "license": "MIT", "packageManager": "pnpm@10.13.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "optionalDependencies": {"@loglayer/plugin-redaction": "^2.0.0", "@loglayer/transport-simple-pretty-terminal": "^2.0.0"}, "devDependencies": {"@alicloud/log": "^1.2.5", "@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.5", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.0.0", "ts-jest": "^29.0.0", "tsup": "^8.0.0", "turbo": "^2.5.5", "typescript": "^5.0.0"}, "repository": {"type": "git", "url": "https://github.com/yai-nexus/loglayer-support.git"}, "bugs": {"url": "https://github.com/yai-nexus/loglayer-support/issues"}, "homepage": "https://github.com/yai-nexus/loglayer-support#readme", "publishConfig": {"access": "public"}}