{"name": "@yai-loglayer/sls-transport", "version": "0.8.0", "description": "阿里云 SLS (Simple Log Service) transport for LogLayer - 企业级日志传输组件", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0"}, "keywords": ["logging", "logger", "loglayer", "sls", "alicloud", "<PERSON><PERSON><PERSON>", "transport", "batch", "retry"], "dependencies": {"@yai-loglayer/core": "workspace:*", "loglayer": "^6.6.0", "@loglayer/transport": "^2.2.1"}, "devDependencies": {"@yai-loglayer/build-config": "workspace:*", "@yai-loglayer/eslint-config": "workspace:*", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "jest": "^29.0.0", "ts-jest": "^29.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/yai-nexus/loglayer-support.git", "directory": "packages/sls-transport"}, "bugs": {"url": "https://github.com/yai-nexus/loglayer-support/issues"}, "homepage": "https://github.com/yai-nexus/loglayer-support/tree/main/packages/sls-transport#readme"}