{"name": "@yai-loglayer/browser", "version": "0.8.0", "description": "浏览器端的 loglayer 封装，提供开箱即用的日志解决方案", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@yai-loglayer/core": "workspace:*", "@loglayer/transport": "^2.2.1", "loglayer": "^6.6.0"}, "devDependencies": {"@yai-loglayer/build-config": "workspace:*", "@yai-loglayer/eslint-config": "workspace:*", "@types/jest": "^29.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^30.0.5", "ts-jest": "^29.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "publishConfig": {"access": "public"}}