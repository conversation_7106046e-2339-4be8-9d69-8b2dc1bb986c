{"name": "@yai-loglayer/receiver", "version": "0.8.0", "description": "日志接收器，用于接收和处理来自客户端的日志数据", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@yai-loglayer/core": "workspace:*", "@alicloud/log": "^1.2.5", "loglayer": "^6.6.0"}, "devDependencies": {"@yai-loglayer/build-config": "workspace:*", "@yai-loglayer/eslint-config": "workspace:*", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "jest": "^29.0.0", "ts-jest": "^29.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "publishConfig": {"access": "public"}}