# 构建和配置文件（root 不再有源代码）
.eslintrc.*
.prettierrc.*
.prettierignore

# 测试文件
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js
test/
tests/
__tests__/
jest.config.*

# 示例和文档（这些在 package.json files 中已经明确包含的除外）
examples/
docs/

# Demo 目录
demo/

# 开发工具配置
.vscode/
.idea/
*.sublime-*

# 环境和临时文件
.env
.env.*
*.log
.DS_Store
Thumbs.db

# Git 相关
.git/
.gitignore
.gitattributes

# CI/CD 配置
.github/
.gitlab-ci.yml
.travis.yml
circle.yml

# 依赖和缓存
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 覆盖率报告
coverage/
.nyc_output/

# 备份文件
*.bak
*.backup
*.tmp

# 其他开发文件
.editorconfig
nodemon.json