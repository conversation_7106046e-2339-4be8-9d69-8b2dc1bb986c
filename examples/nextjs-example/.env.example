# LogLayer Basic Example 环境变量配置
# 复制此文件为 .env 并填写实际配置值

# ==================== 阿里云 SLS 配置 ====================
# SLS 服务端点（根据你的阿里云地域选择）
# 华东1（杭州）: https://cn-hangzhou.log.aliyuncs.com
# 华北2（北京）: https://cn-beijing.log.aliyuncs.com
# 华东2（上海）: https://cn-shanghai.log.aliyuncs.com
SLS_ENDPOINT=https://cn-hangzhou.log.aliyuncs.com

# SLS 项目名称（在阿里云 SLS 控制台创置）
SLS_PROJECT=your-project-name

# SLS 日志库名称（在项目下创建的日志库）
SLS_LOGSTORE=your-logstore-name

# 阿里云访问密钥 ID（RAM 用户或主账号）
SLS_ACCESS_KEY_ID=your-access-key-id

# 阿里云访问密钥密码
SLS_ACCESS_KEY_SECRET=your-access-key-secret

# 应用名称（用于标识日志来源）
SLS_APP_NAME=loglayer-basic-example

# ==================== 其他配置 ====================
# Node.js 环境
NODE_ENV=development

# 日志级别
LOG_LEVEL=debug
