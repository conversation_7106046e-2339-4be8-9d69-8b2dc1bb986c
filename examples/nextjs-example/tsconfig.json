{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "es6", "es2015"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "downlevelIteration": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "loglayer-support": ["../../src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}