{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./", "resolveJsonModule": true, "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["./lib/*"], "@details/*": ["./details/*"]}}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"]}