{"name": "react-example", "version": "0.8.0", "description": "React example demonstrating @yai-loglayer/browser usage", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "clean": "rm -rf dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@yai-loglayer/browser": "workspace:*", "@yai-loglayer/core": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0"}, "engines": {"node": ">=18.0.0"}}