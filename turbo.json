{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**"], "env": ["NODE_ENV"]}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "lint": {"outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}}, "globalDependencies": ["tsconfig.json", "turbo.json"]}